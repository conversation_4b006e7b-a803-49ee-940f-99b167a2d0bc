import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:escape/theme/app_constants.dart';
import 'package:escape/models/streak_model.dart';
import 'package:escape/repositories/streak_repository.dart';
import '../widgets/history_item_card.dart';

class StreakHistoryScreen extends ConsumerStatefulWidget {
  const StreakHistoryScreen({super.key});

  @override
  ConsumerState<StreakHistoryScreen> createState() =>
      _StreakHistoryScreenState();
}

class _StreakHistoryScreenState extends ConsumerState<StreakHistoryScreen> {
  List<Streak> _streaks = [];
  List<Streak> _filteredStreaks = [];
  Map<String, dynamic>? _statistics;
  bool _isLoading = false;
  String _searchQuery = '';
  DateTime? _selectedDate;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final streakRepository = ref.read(streakRepositoryProvider.notifier);

      // Load streaks with pagination
      final streaks = await streakRepository.getStreaksWithPagination(
        limit: 100,
        startDate: _selectedDate,
        endDate: _selectedDate,
      );

      // Load statistics
      final stats = await streakRepository.getStreakStatistics(
        startDate:
            _selectedDate ?? DateTime.now().subtract(const Duration(days: 30)),
        endDate: _selectedDate ?? DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _streaks = streaks;
          _filteredStreaks = streaks;
          _statistics = stats;
        });
        _applyFilters();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading streak history: $e'),
            backgroundColor: AppConstants.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredStreaks = _streaks.where((streak) {
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!streak.count.toString().contains(query) &&
              !streak.isSuccess.toString().toLowerCase().contains(query)) {
            return false;
          }
        }
        return true;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
      _loadData();
    }
  }

  Future<void> _editStreak(Streak streak) async {
    final result = await showDialog<Streak>(
      context: context,
      builder: (context) => _StreakEditDialog(streak: streak),
    );

    if (result != null) {
      try {
        final streakRepository = ref.read(streakRepositoryProvider.notifier);
        await streakRepository.updateStreak(result);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Streak updated successfully'),
              backgroundColor: AppConstants.primaryGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating streak: $e'),
              backgroundColor: AppConstants.errorRed,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteStreak(Streak streak) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Streak'),
        content: Text(
          'Are you sure you want to delete the streak record for ${streak.date.day}/${streak.date.month}/${streak.date.year}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppConstants.errorRed),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final streakRepository = ref.read(streakRepositoryProvider.notifier);
        await streakRepository.deleteStreak(streak.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Streak deleted successfully'),
              backgroundColor: AppConstants.primaryGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting streak: $e'),
              backgroundColor: AppConstants.errorRed,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Streak History'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'Select Date',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'success_only':
                  setState(() {
                    _filteredStreaks = _streaks
                        .where((s) => s.isSuccess)
                        .toList();
                  });
                  break;
                case 'relapses_only':
                  setState(() {
                    _filteredStreaks = _streaks
                        .where((s) => !s.isSuccess)
                        .toList();
                  });
                  break;
                case 'all':
                  setState(() {
                    _filteredStreaks = _streaks;
                  });
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('Show All')),
              const PopupMenuItem(
                value: 'success_only',
                child: Text('Success Only'),
              ),
              const PopupMenuItem(
                value: 'relapses_only',
                child: Text('Relapses Only'),
              ),
            ],
            child: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.spacingM),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search streaks...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_filteredStreaks.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        itemCount: _filteredStreaks.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildStatisticsCard();
          }

          final streak = _filteredStreaks[index - 1];
          return HistoryItemCard(
            title: 'Day ${streak.count}',
            subtitle: streak.isSuccess ? 'Success' : 'Relapse',
            date: streak.date,
            icon: streak.isSuccess ? Icons.check_circle : Icons.cancel,
            iconColor: streak.isSuccess
                ? AppConstants.primaryGreen
                : AppConstants.errorRed,
            isSuccess: streak.isSuccess,
            onEdit: () => _editStreak(streak),
            onDelete: () => _deleteStreak(streak),
            additionalInfo: [
              Wrap(
                spacing: AppConstants.spacingS,
                children: [
                  HistoryInfoChip(
                    label: 'Count',
                    value: streak.count.toString(),
                    icon: Icons.local_fire_department,
                    color: AppConstants.primaryGreen,
                  ),
                  HistoryInfoChip(
                    label: 'Mood',
                    value: '${streak.moodIntensity}/10',
                    icon: Icons.mood,
                    color: AppConstants.lightGreen,
                  ),
                  if (streak.isGoalAchieved)
                    HistoryInfoChip(
                      label: 'Goal',
                      value: 'Achieved',
                      icon: Icons.star,
                      color: AppConstants.successGreen,
                    ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatisticsCard() {
    if (_statistics == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(AppConstants.spacingM),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.spacingM),
            Wrap(
              spacing: AppConstants.spacingS,
              runSpacing: AppConstants.spacingS,
              children: [
                HistoryInfoChip(
                  label: 'Total Entries',
                  value: _statistics!['totalEntries'].toString(),
                  icon: Icons.list,
                ),
                HistoryInfoChip(
                  label: 'Success Rate',
                  value: '${_statistics!['successRate'].toStringAsFixed(1)}%',
                  icon: Icons.trending_up,
                  color: AppConstants.primaryGreen,
                ),
                HistoryInfoChip(
                  label: 'Longest Streak',
                  value: _statistics!['longestStreak'].toString(),
                  icon: Icons.local_fire_department,
                  color: AppConstants.warningOrange,
                ),
                HistoryInfoChip(
                  label: 'Max Count',
                  value: _statistics!['maxCount'].toString(),
                  icon: Icons.star,
                  color: AppConstants.successGreen,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_fire_department_outlined,
            size: 64,
            color: AppConstants.mediumGray,
          ),
          const SizedBox(height: AppConstants.spacingM),
          Text(
            'No streak records found',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: AppConstants.mediumGray),
          ),
          const SizedBox(height: AppConstants.spacingS),
          Text(
            'Start tracking your streaks to see history here',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppConstants.mediumGray),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _StreakEditDialog extends StatefulWidget {
  final Streak streak;

  const _StreakEditDialog({required this.streak});

  @override
  State<_StreakEditDialog> createState() => _StreakEditDialogState();
}

class _StreakEditDialogState extends State<_StreakEditDialog> {
  late TextEditingController _countController;
  late TextEditingController _moodController;
  late bool _isSuccess;

  @override
  void initState() {
    super.initState();
    _countController = TextEditingController(
      text: widget.streak.count.toString(),
    );
    _moodController = TextEditingController(
      text: widget.streak.moodIntensity.toString(),
    );
    _isSuccess = widget.streak.isSuccess;
  }

  @override
  void dispose() {
    _countController.dispose();
    _moodController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Streak'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _countController,
              decoration: const InputDecoration(
                labelText: 'Streak Count',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.spacingM),
            TextField(
              controller: _moodController,
              decoration: const InputDecoration(
                labelText: 'Mood Intensity (1-10)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.spacingM),
            SwitchListTile(
              title: const Text('Success'),
              value: _isSuccess,
              onChanged: (value) {
                setState(() {
                  _isSuccess = value;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final count = int.tryParse(_countController.text) ?? 0;
            final mood = int.tryParse(_moodController.text) ?? 0;

            if (count >= 0 && mood >= 1 && mood <= 10) {
              final updatedStreak = widget.streak.copyWith(
                count: count,
                moodIntensity: mood,
                isSuccess: _isSuccess,
              );
              Navigator.of(context).pop(updatedStreak);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter valid values'),
                  backgroundColor: AppConstants.errorRed,
                ),
              );
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
