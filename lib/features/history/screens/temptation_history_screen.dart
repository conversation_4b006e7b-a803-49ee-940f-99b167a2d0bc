import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:escape/theme/app_constants.dart';
import 'package:escape/models/temptation_model.dart';
import 'package:escape/repositories/temptation_repository.dart';
import '../widgets/history_item_card.dart';

class TemptationHistoryScreen extends ConsumerStatefulWidget {
  const TemptationHistoryScreen({super.key});

  @override
  ConsumerState<TemptationHistoryScreen> createState() =>
      _TemptationHistoryScreenState();
}

class _TemptationHistoryScreenState
    extends ConsumerState<TemptationHistoryScreen> {
  List<Temptation> _temptations = [];
  List<Temptation> _filteredTemptations = [];
  Map<String, dynamic>? _statistics;
  bool _isLoading = false;
  String _searchQuery = '';
  DateTime? _selectedDate;
  bool? _successFilter;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final temptationRepository = ref.read(
        temptationRepositoryProvider.notifier,
      );

      final temptations = await temptationRepository
          .getTemptationsWithPagination(
            limit: 100,
            startDate: _selectedDate,
            endDate: _selectedDate,
            wasSuccessful: _successFilter,
            isResolved: true,
          );

      final stats = await temptationRepository.getTemptationStatistics(
        startDate:
            _selectedDate ?? DateTime.now().subtract(const Duration(days: 30)),
        endDate: _selectedDate ?? DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _temptations = temptations;
          _filteredTemptations = temptations;
          _statistics = stats;
        });
        _applyFilters();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading temptation history: $e'),
            backgroundColor: AppConstants.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredTemptations = _temptations.where((temptation) {
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!(temptation.selectedActivity?.toLowerCase().contains(query) ??
                  false) &&
              !temptation.triggers.any(
                (trigger) => trigger.toLowerCase().contains(query),
              ) &&
              !(temptation.resolutionNotes?.toLowerCase().contains(query) ??
                  false)) {
            return false;
          }
        }
        return true;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
      _loadData();
    }
  }

  Future<void> _editTemptation(Temptation temptation) async {
    final result = await showDialog<Temptation>(
      context: context,
      builder: (context) => _TemptationEditDialog(temptation: temptation),
    );

    if (result != null) {
      try {
        final temptationRepository = ref.read(
          temptationRepositoryProvider.notifier,
        );
        await temptationRepository.updateTemptation(result);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Temptation updated successfully'),
              backgroundColor: AppConstants.primaryGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating temptation: $e'),
              backgroundColor: AppConstants.errorRed,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteTemptation(Temptation temptation) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Temptation'),
        content: Text(
          'Are you sure you want to delete this temptation record from ${temptation.createdAt.day}/${temptation.createdAt.month}/${temptation.createdAt.year}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppConstants.errorRed),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final temptationRepository = ref.read(
          temptationRepositoryProvider.notifier,
        );
        await temptationRepository.deleteTemptation(temptation.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Temptation deleted successfully'),
              backgroundColor: AppConstants.primaryGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting temptation: $e'),
              backgroundColor: AppConstants.errorRed,
            ),
          );
        }
      }
    }
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return 'Unknown';

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Temptation History'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'Select Date',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                switch (value) {
                  case 'all':
                    _successFilter = null;
                    break;
                  case 'success':
                    _successFilter = true;
                    break;
                  case 'relapse':
                    _successFilter = false;
                    break;
                }
              });
              _loadData();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('Show All')),
              const PopupMenuItem(
                value: 'success',
                child: Text('Success Only'),
              ),
              const PopupMenuItem(
                value: 'relapse',
                child: Text('Relapses Only'),
              ),
            ],
            child: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.spacingM),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search temptations...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_filteredTemptations.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        itemCount: _filteredTemptations.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildStatisticsCard();
          }

          final temptation = _filteredTemptations[index - 1];
          return HistoryItemCard(
            title: temptation.wasSuccessful
                ? 'Overcame Temptation'
                : 'Relapsed',
            subtitle: temptation.selectedActivity ?? 'No activity selected',
            date: temptation.createdAt,
            icon: temptation.wasSuccessful ? Icons.check_circle : Icons.cancel,
            iconColor: temptation.wasSuccessful
                ? AppConstants.primaryGreen
                : AppConstants.errorRed,
            isSuccess: temptation.wasSuccessful,
            onEdit: () => _editTemptation(temptation),
            onDelete: () => _deleteTemptation(temptation),
            additionalInfo: [
              Wrap(
                spacing: AppConstants.spacingS,
                runSpacing: AppConstants.spacingS,
                children: [
                  if (temptation.duration != null)
                    HistoryInfoChip(
                      label: 'Duration',
                      value: _formatDuration(temptation.duration),
                      icon: Icons.timer,
                      color: AppConstants.lightGreen,
                    ),
                  if (temptation.intensityBefore != null)
                    HistoryInfoChip(
                      label: 'Intensity',
                      value: '${temptation.intensityBefore}/10',
                      icon: Icons.trending_up,
                      color: AppConstants.errorRed,
                    ),
                  if (temptation.triggers.isNotEmpty)
                    HistoryInfoChip(
                      label: 'Triggers',
                      value: temptation.triggers.length.toString(),
                      icon: Icons.warning,
                      color: AppConstants.warningOrange,
                    ),
                  HistoryInfoChip(
                    label: 'XP',
                    value: temptation.wasSuccessful ? '1000' : '200',
                    icon: Icons.star,
                    color: AppConstants.primaryGreen,
                  ),
                ],
              ),
              if (temptation.triggers.isNotEmpty) ...[
                const SizedBox(height: AppConstants.spacingS),
                Text(
                  'Triggers: ${temptation.triggers.join(', ')}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.mediumGray,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
              if (temptation.resolutionNotes != null &&
                  temptation.resolutionNotes!.isNotEmpty) ...[
                const SizedBox(height: AppConstants.spacingS),
                Text(
                  'Notes: ${temptation.resolutionNotes}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.mediumGray,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatisticsCard() {
    if (_statistics == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(AppConstants.spacingM),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Temptation Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.spacingM),
            Wrap(
              spacing: AppConstants.spacingS,
              runSpacing: AppConstants.spacingS,
              children: [
                HistoryInfoChip(
                  label: 'Total',
                  value: _statistics!['totalTemptations'].toString(),
                  icon: Icons.list,
                ),
                HistoryInfoChip(
                  label: 'Success Rate',
                  value: '${_statistics!['successRate'].toStringAsFixed(1)}%',
                  icon: Icons.trending_up,
                  color: AppConstants.primaryGreen,
                ),
                HistoryInfoChip(
                  label: 'Avg Duration',
                  value:
                      '${_statistics!['averageDuration'].toStringAsFixed(0)}m',
                  icon: Icons.timer,
                  color: AppConstants.lightGreen,
                ),
                HistoryInfoChip(
                  label: 'Relapses',
                  value: _statistics!['relapses'].toString(),
                  icon: Icons.cancel,
                  color: AppConstants.errorRed,
                ),
              ],
            ),
            if ((_statistics!['commonTriggers'] as List).isNotEmpty) ...[
              const SizedBox(height: AppConstants.spacingM),
              Text(
                'Common Triggers:',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: AppConstants.spacingS),
              Wrap(
                spacing: AppConstants.spacingXS,
                children: (_statistics!['commonTriggers'] as List<String>)
                    .map(
                      (trigger) => Chip(
                        label: Text(trigger),
                        backgroundColor: AppConstants.warningOrange.withValues(
                          alpha: 0.1,
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 64,
            color: AppConstants.mediumGray,
          ),
          const SizedBox(height: AppConstants.spacingM),
          Text(
            'No temptation records found',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: AppConstants.mediumGray),
          ),
          const SizedBox(height: AppConstants.spacingS),
          Text(
            'Your temptation sessions will appear here once completed',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppConstants.mediumGray),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _TemptationEditDialog extends StatefulWidget {
  final Temptation temptation;

  const _TemptationEditDialog({required this.temptation});

  @override
  State<_TemptationEditDialog> createState() => _TemptationEditDialogState();
}

class _TemptationEditDialogState extends State<_TemptationEditDialog> {
  late TextEditingController _activityController;
  late TextEditingController _notesController;
  late bool _wasSuccessful;
  late List<String> _triggers;

  @override
  void initState() {
    super.initState();
    _activityController = TextEditingController(
      text: widget.temptation.selectedActivity ?? '',
    );
    _notesController = TextEditingController(
      text: widget.temptation.resolutionNotes ?? '',
    );
    _wasSuccessful = widget.temptation.wasSuccessful;
    _triggers = List.from(widget.temptation.triggers);
  }

  @override
  void dispose() {
    _activityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Temptation'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _activityController,
              decoration: const InputDecoration(
                labelText: 'Selected Activity',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.spacingM),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Resolution Notes',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: AppConstants.spacingM),
            SwitchListTile(
              title: const Text('Was Successful'),
              value: _wasSuccessful,
              onChanged: (value) {
                setState(() {
                  _wasSuccessful = value;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedTemptation = widget.temptation.copyWith(
              selectedActivity: _activityController.text.trim().isEmpty
                  ? null
                  : _activityController.text.trim(),
              resolutionNotes: _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
              wasSuccessful: _wasSuccessful,
              triggers: _triggers,
            );
            Navigator.of(context).pop(updatedTemptation);
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
