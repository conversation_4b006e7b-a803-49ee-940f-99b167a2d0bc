import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:escape/theme/app_constants.dart';
import 'package:escape/models/prayer_model.dart';
import 'package:escape/repositories/prayer_repository.dart';
import '../widgets/history_item_card.dart';
import 'base_history_screen.dart';

class PrayerHistoryScreen extends BaseHistoryScreen {
  const PrayerHistoryScreen({super.key})
    : super(
        title: 'Prayer History',
        subtitle: 'View and manage your prayer records',
        icon: Icons.mosque,
      );

  @override
  State<PrayerHistoryScreen> createState() => _PrayerHistoryScreenState();
}

class _PrayerHistoryScreenState
    extends BaseHistoryScreenState<PrayerHistoryScreen> {
  List<Prayer> _prayers = [];
  List<Prayer> _filteredPrayers = [];
  Map<String, dynamic>? _statistics;
  String? _selectedPrayerFilter;

  final List<String> _prayerNames = [
    'Tahajjud',
    'Fajr',
    '<PERSON>huhr',
    'Asr',
    'Maghrib',
    '<PERSON>ha',
  ];

  Future<void> _loadData() async {
    setLoading(true);
    try {
      final prayerRepository = ProviderScope.containerOf(
        context,
      ).read(prayerRepositoryProvider.notifier);

      // Load prayers with pagination
      final prayers = await prayerRepository.getPrayersWithPagination(
        limit: 100,
        startDate: selectedDate,
        endDate: selectedDate,
        prayerName: _selectedPrayerFilter,
      );

      // Load statistics
      final stats = await prayerRepository.getPrayerStatistics(
        startDate: selectedDate ?? DateTime.now().subtract(const Duration(days: 30)),
        endDate: selectedDate ?? DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _prayers = prayers;
          _filteredPrayers = prayers;
          _statistics = stats;
        });
        _applyFilters();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading prayer history: $e'),
            backgroundColor: AppConstants.errorRed,
          ),
        );
      }
    } finally {
      setLoading(false);
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredPrayers = _prayers.where((prayer) {
        // Search filter
        if (searchQuery.isNotEmpty) {
          final query = searchQuery.toLowerCase();
          if (!prayer.name.toLowerCase().contains(query) &&
              !prayer.isCompleted.toString().toLowerCase().contains(query)) {
            return false;
          }
        }

        // Prayer name filter
        if (_selectedPrayerFilter != null &&
            prayer.name != _selectedPrayerFilter) {
          return false;
        }

        return true;
      }).toList();
    });
  }

  @override
  void onSearchChanged(String query) {
    _applyFilters();
  }

  @override
  void onDateSelected(DateTime? date) {
    _loadData(); // Reload data with new date filter
  }

  Future<void> _editPrayer(Prayer prayer) async {
    final result = await showDialog<Prayer>(
      context: context,
      builder: (context) => _PrayerEditDialog(prayer: prayer),
    );

    if (result != null) {
      try {
        final prayerRepository = ProviderScope.containerOf(
          context,
        ).read(prayerRepositoryProvider.notifier);
        await prayerRepository.updatePrayer(result);
        _loadData(); // Refresh the list

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Prayer updated successfully'),
              backgroundColor: AppConstants.primaryGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating prayer: $e'),
              backgroundColor: AppConstants.errorRed,
            ),
          );
        }
      }
    }
  }

  Future<void> _deletePrayer(Prayer prayer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Prayer'),
        content: Text(
          'Are you sure you want to delete the ${prayer.name} prayer record for ${prayer.date.day}/${prayer.date.month}/${prayer.date.year}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppConstants.errorRed),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final prayerRepository = ProviderScope.containerOf(
          context,
        ).read(prayerRepositoryProvider.notifier);
        await prayerRepository.deletePrayer(prayer.id);
        _loadData(); // Refresh the list

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Prayer deleted successfully'),
              backgroundColor: AppConstants.primaryGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting prayer: $e'),
              backgroundColor: AppConstants.errorRed,
            ),
          );
        }
      }
    }
  }

  IconData _getPrayerIcon(String prayerName) {
    switch (prayerName) {
      case 'Tahajjud':
        return Icons.nightlight_round;
      case 'Fajr':
        return Icons.wb_sunny;
      case 'Dhuhr':
        return Icons.wb_sunny_outlined;
      case 'Asr':
        return Icons.wb_twilight;
      case 'Maghrib':
        return Icons.sunny;
      case 'Isha':
        return Icons.nights_stay;
      default:
        return Icons.mosque;
    }
  }

  int _getPrayerXP(String prayerName) {
    switch (prayerName) {
      case 'Fajr':
      case 'Asr':
        return 300;
      case 'Tahajjud':
        return 5000;
      default:
        return 100;
    }
  }

  @override
  Widget buildHistoryList() {
    if (_filteredPrayers.isEmpty) {
      return buildEmptyState();
    }

    return ListView.builder(
      itemCount: _filteredPrayers.length + 1, // +1 for statistics card
      itemBuilder: (context, index) {
        if (index == 0) {
          // Statistics card
          return _buildStatisticsCard();
        }

        final prayer = _filteredPrayers[index - 1];
        return HistoryItemCard(
          title: prayer.name,
          subtitle: prayer.isCompleted ? 'Completed' : 'Missed',
          date: prayer.date,
          icon: _getPrayerIcon(prayer.name),
          iconColor: prayer.isCompleted
              ? AppConstants.primaryGreen
              : AppConstants.errorRed,
          isSuccess: prayer.isCompleted,
          onEdit: () => _editPrayer(prayer),
          onDelete: () => _deletePrayer(prayer),
          additionalInfo: [
            Wrap(
              spacing: AppConstants.spacingS,
              children: [
                HistoryInfoChip(
                  label: 'XP',
                  value: '${_getPrayerXP(prayer.name)}',
                  icon: Icons.star,
                  color: Colors.blue,
                ),
                HistoryInfoChip(
                  label: 'Status',
                  value: prayer.isCompleted ? 'Completed' : 'Missed',
                  icon: prayer.isCompleted ? Icons.check_circle : Icons.cancel,
                  color: prayer.isCompleted
                      ? AppConstants.primaryGreen
                      : AppConstants.errorRed,
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatisticsCard() {
    if (_statistics == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(AppConstants.spacingM),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prayer Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.spacingM),
            Wrap(
              spacing: AppConstants.spacingS,
              runSpacing: AppConstants.spacingS,
              children: [
                HistoryInfoChip(
                  label: 'Total',
                  value: _statistics!['totalPrayers'].toString(),
                  icon: Icons.list,
                ),
                HistoryInfoChip(
                  label: 'Completed',
                  value: _statistics!['completedPrayers'].toString(),
                  icon: Icons.check_circle,
                  color: AppConstants.primaryGreen,
                ),
                HistoryInfoChip(
                  label: 'Missed',
                  value: _statistics!['missedPrayers'].toString(),
                  icon: Icons.cancel,
                  color: AppConstants.errorRed,
                ),
                HistoryInfoChip(
                  label: 'Rate',
                  value:
                      '${_statistics!['completionRate'].toStringAsFixed(1)}%',
                  icon: Icons.trending_up,
                  color: Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.mosque_outlined, size: 64, color: AppConstants.mediumGray),
          const SizedBox(height: AppConstants.spacingM),
          Text(
            'No prayer records found',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: AppConstants.mediumGray),
          ),
          const SizedBox(height: AppConstants.spacingS),
          Text(
            'Start tracking your prayers to see history here',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppConstants.mediumGray),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  List<Widget> buildFilterActions() {
    return [
      PopupMenuButton<String>(
        onSelected: (value) {
          setState(() {
            if (value == 'all') {
              _selectedPrayerFilter = null;
            } else {
              _selectedPrayerFilter = value;
            }
          });
          _applyFilters();
        },
        itemBuilder: (context) => [
          const PopupMenuItem(value: 'all', child: Text('All Prayers')),
          ..._prayerNames.map(
            (name) => PopupMenuItem(value: name, child: Text(name)),
          ),
        ],
        child: const Icon(Icons.filter_list),
      ),
    ];
  }
}

class _PrayerEditDialog extends StatefulWidget {
  final Prayer prayer;

  const _PrayerEditDialog({required this.prayer});

  @override
  State<_PrayerEditDialog> createState() => _PrayerEditDialogState();
}

class _PrayerEditDialogState extends State<_PrayerEditDialog> {
  late String _selectedPrayerName;
  late bool _isCompleted;

  final List<String> _prayerNames = [
    'Tahajjud',
    'Fajr',
    'Dhuhr',
    'Asr',
    'Maghrib',
    'Isha',
  ];

  @override
  void initState() {
    super.initState();
    _selectedPrayerName = widget.prayer.name;
    _isCompleted = widget.prayer.isCompleted;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Prayer'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedPrayerName,
              decoration: const InputDecoration(
                labelText: 'Prayer Name',
                border: OutlineInputBorder(),
              ),
              items: _prayerNames
                  .map(
                    (name) => DropdownMenuItem(value: name, child: Text(name)),
                  )
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPrayerName = value;
                  });
                }
              },
            ),
            const SizedBox(height: AppConstants.spacingM),
            SwitchListTile(
              title: const Text('Completed'),
              value: _isCompleted,
              onChanged: (value) {
                setState(() {
                  _isCompleted = value;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedPrayer = widget.prayer.copyWith(
              name: _selectedPrayerName,
              isCompleted: _isCompleted,
            );
            Navigator.of(context).pop(updatedPrayer);
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
